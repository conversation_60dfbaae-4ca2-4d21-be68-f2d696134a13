version: "3.9"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 2
    ports:
      - "8000:8000"
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      SD_MODEL_PATH: /models/sdxl_lightning
      PERSONA_DIR: /data/personas
      OUTPUT_DIR: /data/outputs
      CUDA_VISIBLE_DEVICES: "0"
    volumes:
      - ./data:/data
      - ./models:/models:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: ["gpu"]
    depends_on:
      - redis

  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    command: celery -A app.worker worker --loglevel=INFO --concurrency=1 -Q default -n gpu_worker@%h
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      SD_MODEL_PATH: /models/sdxl_lightning
      PERSONA_DIR: /data/personas
      OUTPUT_DIR: /data/outputs
      CUDA_VISIBLE_DEVICES: "0"
    volumes:
      - ./data:/data
      - ./models:/models:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: ["gpu"]
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  data:
  models:
