"""
Setup script for Image Generation Service
"""

from setuptools import setup, find_packages

try:
    with open("README.md", "r", encoding="utf-8") as fh:
        long_description = fh.read()
except FileNotFoundError:
    long_description = "GPU-accelerated image generation micro-service"

# Define requirements directly instead of reading from file
requirements = [
    "fastapi==0.111.0",
    "uvicorn[standard]==0.30.0",
    "celery==5.4.0",
    "redis==5.0.4",
    "diffusers==0.30.1",
    "transformers==4.42.1",
    "accelerate==0.31.0",
    "pillow>=10.0.0",
    "torch==2.2.2",
    "torchvision==0.17.2",
    "torchaudio==2.2.2",
]

setup(
    name="ig-image-service",
    version="1.0.0",
    author="Image Generation Service Team",
    author_email="<EMAIL>",
    description="GPU-accelerated image generation micro-service using SDXL Lightning and InstantID",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/ig-image-service",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Multimedia :: Graphics",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "gpu": [
            "torch==2.2.2",
            "torchvision==0.17.2",
            "torchaudio==2.2.2",
        ]
    },
    entry_points={
        "console_scripts": [
            "ig-service-api=app.main:main",
            "ig-service-worker=app.worker:main",
            "ig-demo-client=scripts.demo_add_task:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yml", "*.yaml", "*.json", "*.txt", "*.md"],
    },
    zip_safe=False,
)
