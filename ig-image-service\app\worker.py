"""
Celery worker for image generation using diffusers/SDXL Lightning.
"""

from celery import Celery
import os
import torch
import logging
from datetime import datetime
from pathlib import Path
import json
import traceback
from typing import Dict, Any, Optional
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment configuration
SD_MODEL_PATH = os.getenv("SD_MODEL_PATH", "/models/sdxl_lightning")
PERSONA_DIR = os.getenv("PERSONA_DIR", "/data/personas")
OUTPUT_DIR = os.getenv("OUTPUT_DIR", "/data/outputs")
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

# Ensure output directory exists
Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)

# Celery configuration
celery_app = Celery(
    "image_generation",
    broker=os.getenv("CELERY_BROKER_URL", "redis://redis:6379/0"),
    backend=os.getenv("CELERY_RESULT_BACKEND", "redis://redis:6379/1")
)

# Configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes max per task
    worker_prefetch_multiplier=1,  # Process one task at a time
)

# Global model cache
_model_cache = {}


def load_diffusion_model():
    """Load and cache the diffusion model."""
    global _model_cache

    if "pipeline" in _model_cache:
        return _model_cache["pipeline"]

    try:
        logger.info("Loading diffusion model...")

        # Try to import diffusers
        try:
            from diffusers import DiffusionPipeline, StableDiffusionXLPipeline
            import torch
        except ImportError as e:
            logger.error(f"Failed to import diffusers: {e}")
            return None

        # Check if model path exists
        model_path = Path(SD_MODEL_PATH)
        if model_path.exists() and any(model_path.iterdir()):
            logger.info(f"Loading model from {SD_MODEL_PATH}")
            pipeline = StableDiffusionXLPipeline.from_pretrained(
                SD_MODEL_PATH,
                torch_dtype=torch.float16 if DEVICE == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if DEVICE == "cuda" else None
            )
        else:
            logger.info("Model path not found, using high-quality SDXL model")
            # Use a high-quality SDXL model optimized for detailed generation
            pipeline = StableDiffusionXLPipeline.from_pretrained(
                "stabilityai/stable-diffusion-xl-base-1.0",
                torch_dtype=torch.float16 if DEVICE == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if DEVICE == "cuda" else None
            )

        # Move to device and optimize
        pipeline = pipeline.to(DEVICE)

        if DEVICE == "cuda":
            # Enable memory efficient attention
            try:
                pipeline.enable_xformers_memory_efficient_attention()
            except Exception:
                logger.warning("xformers not available, using default attention")

            # Enable CPU offload if needed
            try:
                pipeline.enable_model_cpu_offload()
            except Exception:
                pass

        # Use DPM++ 2M Karras scheduler for better quality
        try:
            from diffusers import DPMSolverMultistepScheduler
            pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                pipeline.scheduler.config,
                use_karras_sigmas=True,
                algorithm_type="dpmsolver++"
            )
            logger.info("Using DPM++ 2M Karras scheduler for better quality")
        except Exception as e:
            logger.warning(f"Could not set DPM++ scheduler: {e}, using default")

        _model_cache["pipeline"] = pipeline
        logger.info(f"Model loaded successfully on {DEVICE}")
        return pipeline

    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        logger.error(traceback.format_exc())
        return None


def generate_filename(task_data: Dict[str, Any]) -> str:
    """Generate a unique filename for the output image."""
    # Create a hash of the task parameters for uniqueness
    task_str = json.dumps({
        "prompt": task_data.get("prompt", ""),
        "width": task_data.get("width", 512),
        "height": task_data.get("height", 512),
        "seed": task_data.get("seed"),
        "guidance_scale": task_data.get("guidance_scale", 1.0),
        "num_inference_steps": task_data.get("num_inference_steps", 4)
    }, sort_keys=True)

    task_hash = hashlib.md5(task_str.encode()).hexdigest()[:8]
    task_id = task_data.get("task_id", "unknown")[:8]

    return f"generated_{task_id}_{task_hash}.png"


def load_persona_config(persona_name: str) -> Dict[str, Any]:
    """Load persona configuration if available."""
    if not persona_name or persona_name == "default":
        return {}

    persona_file = Path(PERSONA_DIR) / f"{persona_name}.json"
    if persona_file.exists():
        try:
            with open(persona_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load persona {persona_name}: {e}")

    return {}


@celery_app.task(name="app.worker.generate_image", bind=True)
def generate_image(self, task_data: Dict[str, Any]):
    """
    Generate image using SDXL Lightning or Stable Diffusion XL.
    """
    start_time = datetime.utcnow()
    task_id = task_data.get("task_id", self.request.id)

    logger.info(f"Starting image generation task {task_id}")
    logger.info(f"Task data: {json.dumps(task_data, indent=2)}")

    try:
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'loading_model', 'progress': 10}
        )

        # Load the diffusion model
        pipeline = load_diffusion_model()
        if pipeline is None:
            raise Exception("Failed to load diffusion model")

        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'preparing_generation', 'progress': 30}
        )

        # Extract parameters
        prompt = task_data.get("prompt", "")
        negative_prompt = task_data.get("negative_prompt", "")
        width = task_data.get("width", 1024)
        height = task_data.get("height", 1024)
        num_inference_steps = task_data.get("num_inference_steps", 30)  # Support ultra-high quality
        guidance_scale = task_data.get("guidance_scale", 8.0)  # Support ultra-high guidance
        seed = task_data.get("seed")
        persona = task_data.get("persona", "default")

        # Load persona configuration
        persona_config = load_persona_config(persona)

        # Apply persona modifications to prompt
        if persona_config:
            if "prompt_prefix" in persona_config:
                prompt = f"{persona_config['prompt_prefix']} {prompt}"
            if "prompt_suffix" in persona_config:
                prompt = f"{prompt} {persona_config['prompt_suffix']}"
            if "negative_prompt" in persona_config:
                if negative_prompt:
                    negative_prompt = f"{negative_prompt}, {persona_config['negative_prompt']}"
                else:
                    negative_prompt = persona_config['negative_prompt']

        # Enhance prompt for better quality
        quality_terms = "high quality, detailed, sharp focus, professional"
        if quality_terms not in prompt.lower():
            prompt = f"{prompt}, {quality_terms}"

        # Enhance negative prompt for better quality
        quality_negative = "blurry, low quality, distorted, pixelated, artifacts, noise"
        if negative_prompt:
            if not any(term in negative_prompt.lower() for term in ["blurry", "low quality"]):
                negative_prompt = f"{negative_prompt}, {quality_negative}"
        else:
            negative_prompt = quality_negative

        # Set up generator for reproducible results
        generator = None
        if seed is not None:
            generator = torch.Generator(device=DEVICE).manual_seed(seed)

        logger.info(f"Generating image with prompt: '{prompt}'")
        logger.info(f"Parameters: {width}x{height}, steps={num_inference_steps}, guidance={guidance_scale}")

        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'generating_image', 'progress': 50}
        )

        # Generate the image
        with torch.inference_mode():
            result = pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt if negative_prompt else None,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                generator=generator,
                return_dict=True
            )

        image = result.images[0]

        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'saving_image', 'progress': 80}
        )

        # Generate filename and save image
        filename = generate_filename(task_data)
        output_path = Path(OUTPUT_DIR) / filename

        # Handle encryption if requested
        encrypt = task_data.get("encrypt", False)
        encryption_key = task_data.get("encryption_key")

        if encrypt and encryption_key:
            # Save image to memory buffer first
            from io import BytesIO
            import base64
            from cryptography.fernet import Fernet

            # Convert image to bytes
            img_buffer = BytesIO()
            image.save(img_buffer, format='PNG', optimize=True)
            img_bytes = img_buffer.getvalue()

            # Encrypt the image data
            key = base64.urlsafe_b64encode(base64.b64decode(encryption_key))
            fernet = Fernet(key)
            encrypted_data = fernet.encrypt(img_bytes)

            # Save encrypted data to file
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)

            logger.info(f"Image encrypted and saved to: {output_path}")
        else:
            # Save unencrypted image
            image.save(output_path, format='PNG', optimize=True)
            logger.info(f"Image saved to: {output_path}")

        # Calculate generation time
        end_time = datetime.utcnow()
        generation_time = (end_time - start_time).total_seconds()

        logger.info(f"Image generation completed in {generation_time:.2f}s")
        logger.info(f"Image saved to: {output_path}")

        # Prepare result
        result_data = {
            "status": "completed",
            "task_id": task_id,
            "filename": filename,
            "output_path": str(output_path),
            "generation_time": generation_time,
            "parameters": {
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "width": width,
                "height": height,
                "num_inference_steps": num_inference_steps,
                "guidance_scale": guidance_scale,
                "seed": seed,
                "persona": persona,
                "encrypt": encrypt,
                "encryption_key": encryption_key
            },
            "system_info": {
                "device": DEVICE,
                "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
                "gpu_memory_used": torch.cuda.memory_allocated(0) if torch.cuda.is_available() else None
            },
            "created_at": task_data.get("created_at"),
            "completed_at": end_time.isoformat()
        }

        return result_data

    except Exception as e:
        error_msg = str(e)
        error_trace = traceback.format_exc()

        logger.error(f"Image generation failed for task {task_id}: {error_msg}")
        logger.error(f"Traceback: {error_trace}")

        # Calculate time even for failed tasks
        end_time = datetime.utcnow()
        generation_time = (end_time - start_time).total_seconds()

        return {
            "status": "failed",
            "task_id": task_id,
            "error": error_msg,
            "error_trace": error_trace,
            "generation_time": generation_time,
            "created_at": task_data.get("created_at"),
            "failed_at": end_time.isoformat()
        }


def main():
    """Main entry point for the worker service."""
    celery_app.start()


if __name__ == "__main__":
    main()
