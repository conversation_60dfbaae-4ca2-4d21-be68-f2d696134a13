#!/usr/bin/env python3
"""
Enhanced Workflow Test - Tests the new API workflow:
1. Create multiple tasks (without encryption for now)
2. Monitor task status updates
3. Download results when completed
"""

import requests
import time
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import json


def create_test_folder() -> str:
    """Create a timestamped folder for workflow tests."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_folder = f"data/workflow_tests/workflow_{timestamp}"
    Path(test_folder).mkdir(parents=True, exist_ok=True)
    return test_folder


def create_task(prompt: str, negative_prompt: str, steps: int, guidance: float, seed: int) -> Dict[str, Any]:
    """Create a single image generation task."""
    task_data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": 1080,
        "height": 1080,
        "num_inference_steps": steps,
        "guidance_scale": guidance,
        "seed": seed,
        "persona": "default",
        "encrypt": True  # Enable encryption for testing
    }
    
    try:
        response = requests.post("http://localhost:8000/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            return {
                "success": True,
                "task_id": result["task_id"],
                "status": result["status"],
                "encrypted": result.get("encrypted", False),
                "encryption_key": result.get("encryption_key"),
                "prompt": prompt,
                "steps": steps,
                "guidance": guidance,
                "seed": seed
            }
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
                "prompt": prompt
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "prompt": prompt
        }


def check_task_status(task_id: str) -> Dict[str, Any]:
    """Check the status of a task."""
    try:
        response = requests.get(f"http://localhost:8000/tasks/{task_id}", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"status": "error", "error": str(e)}


def download_image(task_id: str, test_folder: str, task_info: Dict[str, Any]) -> bool:
    """Download the completed image (with decryption support)."""
    try:
        # Check if the task was encrypted and get the decryption key
        decryption_key = task_info.get('encryption_key')

        # Prepare download URL with decryption key if needed
        if decryption_key:
            response = requests.get(
                f"http://localhost:8000/tasks/{task_id}/download",
                params={"decryption_key": decryption_key},
                timeout=30
            )
            print(f"   🔐 Downloading encrypted image with decryption key...")
        else:
            response = requests.get(f"http://localhost:8000/tasks/{task_id}/download", timeout=30)
            print(f"   📥 Downloading unencrypted image...")

        if response.status_code == 200:
            # Save the image
            filename = f"task_{task_id}_{task_info['seed']}.png"
            file_path = Path(test_folder) / filename

            with open(file_path, 'wb') as f:
                f.write(response.content)

            print(f"   ✅ Downloaded: {filename}")
            return True
        else:
            print(f"   ❌ Download failed: HTTP {response.status_code}")
            if response.status_code == 400:
                print(f"   📄 Error details: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Download error: {e}")
        return False


def run_enhanced_workflow_test():
    """Run the enhanced workflow test."""
    
    print("🚀 ENHANCED WORKFLOW TEST")
    print("📋 Testing: Create → Monitor → Download")
    print("=" * 50)
    
    # Create test folder
    test_folder = create_test_folder()
    print(f"📁 Test folder: {test_folder}")
    print()
    
    # Define test tasks
    test_tasks = [
        {
            "name": "Portrait",
            "prompt": "professional portrait of young woman with silver hair, elegant dress, studio lighting",
            "negative_prompt": "cartoon, anime, painting, low quality, blurry",
            "steps": 50,
            "guidance": 8.0,
            "seed": 9001
        }

        # {
        #     "name": "Portrait",
        #     "prompt": "professional portrait of elderly man with silver beard, elegant suit, studio lighting",
        #     "negative_prompt": "cartoon, anime, painting, low quality, blurry",
        #     "steps": 35,
        #     "guidance": 8.0,
        #     "seed": 9001
        # },
        # {
        #     "name": "Nature",
        #     "prompt": "macro photograph of red rose with morning dew, professional photography, detailed petals",
        #     "negative_prompt": "artificial, cartoon, painting, low quality, blurry",
        #     "steps": 30,
        #     "guidance": 7.5,
        #     "seed": 9002
        # },
        # {
        #     "name": "Landscape",
        #     "prompt": "mountain landscape with alpine lake at sunset, dramatic clouds, professional photography",
        #     "negative_prompt": "cartoon, painting, low quality, blurry, oversaturated",
        #     "steps": 40,
        #     "guidance": 8.5,
        #     "seed": 9003
        # },
        # {
        #     "name": "Architecture",
        #     "prompt": "modern glass building with geometric patterns, reflections, dramatic sky, architectural photography",
        #     "negative_prompt": "blurry, distorted, cartoon, low quality, people, cars",
        #     "steps": 25,
        #     "guidance": 7.0,
        #     "seed": 9004
        # }
    ]
    
    print("📤 STEP 1: Creating Tasks")
    print("-" * 30)
    
    # Create all tasks
    created_tasks = []
    for i, task_def in enumerate(test_tasks, 1):
        print(f"[{i}/4] Creating {task_def['name']} task...")
        
        task_result = create_task(
            prompt=task_def["prompt"],
            negative_prompt=task_def["negative_prompt"],
            steps=task_def["steps"],
            guidance=task_def["guidance"],
            seed=task_def["seed"]
        )
        
        if task_result["success"]:
            print(f"   ✅ Task ID: {task_result['task_id']}")
            created_tasks.append({**task_result, "name": task_def["name"]})
        else:
            print(f"   ❌ Failed: {task_result['error']}")
    
    if not created_tasks:
        print("❌ No tasks created successfully!")
        return False
    
    print(f"\n✅ Created {len(created_tasks)}/{len(test_tasks)} tasks successfully")
    print()
    
    print("⏳ STEP 2: Monitoring Task Progress")
    print("-" * 40)
    
    # Monitor tasks until completion
    completed_tasks = []
    max_wait_time = 600  # 10 minutes
    start_time = time.time()
    
    while len(completed_tasks) < len(created_tasks) and (time.time() - start_time) < max_wait_time:
        for task in created_tasks:
            if task["task_id"] in [t["task_id"] for t in completed_tasks]:
                continue  # Already completed
            
            status_info = check_task_status(task["task_id"])
            current_status = status_info.get("status", "unknown")
            
            if current_status == "completed":
                elapsed = time.time() - start_time
                print(f"✅ {task['name']} completed ({elapsed:.1f}s)")
                completed_tasks.append(task)
            elif current_status == "failed":
                error = status_info.get("error", "Unknown error")
                print(f"❌ {task['name']} failed: {error}")
                completed_tasks.append({**task, "failed": True})
            elif current_status in ["processing", "queued"]:
                # Still running, continue monitoring
                pass
            else:
                print(f"⚠️  {task['name']} status: {current_status}")
        
        if len(completed_tasks) < len(created_tasks):
            time.sleep(5)  # Check every 5 seconds
    
    successful_tasks = [t for t in completed_tasks if not t.get("failed", False)]
    print(f"\n📊 Completion Status: {len(successful_tasks)}/{len(created_tasks)} successful")
    print()
    
    if not successful_tasks:
        print("❌ No tasks completed successfully!")
        return False
    
    print("📥 STEP 3: Downloading Results")
    print("-" * 35)
    
    # Download all completed images
    downloaded_count = 0
    for task in successful_tasks:
        print(f"📥 Downloading {task['name']}...")
        if download_image(task["task_id"], test_folder, task):
            downloaded_count += 1
        time.sleep(1)  # Brief pause between downloads
    
    print()
    print("🏆 WORKFLOW TEST RESULTS")
    print("=" * 30)
    print(f"Tasks Created: {len(created_tasks)}/{len(test_tasks)}")
    print(f"Tasks Completed: {len(successful_tasks)}/{len(created_tasks)}")
    print(f"Images Downloaded: {downloaded_count}/{len(successful_tasks)}")
    print(f"Overall Success Rate: {(downloaded_count/len(test_tasks))*100:.1f}%")
    
    if downloaded_count == len(test_tasks):
        print("🎉 All workflow steps completed successfully!")
        print(f"📁 Results in: {test_folder}")
        
        # List downloaded files
        downloaded_files = list(Path(test_folder).glob("*.png"))
        print(f"📊 Downloaded {len(downloaded_files)} images:")
        for file_path in downloaded_files:
            print(f"   🖼️  {file_path.name}")
        
        return True
    else:
        print("⚠️  Workflow partially completed")
        return False


def main():
    """Main entry point."""
    try:
        success = run_enhanced_workflow_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
