#!/usr/bin/env python3
"""
Premium Quality Test Script for Ultra-High Quality Image Generation.
Uses optimized parameters for maximum quality results.
"""

import requests
import time
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any


def create_premium_test_folder() -> str:
    """Create a timestamped folder for premium quality tests."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_folder = f"data/premium_tests/premium_{timestamp}"
    Path(test_folder).mkdir(parents=True, exist_ok=True)
    return test_folder


def generate_premium_image(prompt: str, negative_prompt: str = "", steps: int = 60, guidance: float = 10.0, seed: int = None, test_folder: str = None) -> tuple[bool, str]:
    """Generate a premium quality image with optimized parameters."""
    
    # Enhanced negative prompt for premium quality
    premium_negative = "blurry, low quality, distorted, pixelated, artifacts, noise, oversaturated, unrealistic, amateur, watermark, signature, text"
    if negative_prompt:
        negative_prompt = f"{negative_prompt}, {premium_negative}"
    else:
        negative_prompt = premium_negative
    
    task_data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": 1024,
        "height": 1024,
        "num_inference_steps": steps,
        "guidance_scale": guidance,
        "seed": seed,
        "persona": "default"
    }
    
    print(f"🎨 Premium Quality Generation")
    print(f"   Prompt: '{prompt[:80]}{'...' if len(prompt) > 80 else ''}'")
    print(f"   Steps: {steps}, Guidance: {guidance}, Seed: {seed}")
    print("-" * 60)
    
    try:
        # Create task
        print("📤 Submitting premium task...")
        response = requests.post("http://localhost:8000/tasks", json=task_data, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to create task: {response.status_code}")
            return False, None
            
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Task created: {task_id}")
        
        # Monitor progress with detailed tracking
        print("⏳ Generating premium quality image...")
        start_time = time.time()
        
        for i in range(600):  # 10 minutes max for ultra premium quality
            status_response = requests.get(f"http://localhost:8000/tasks/{task_id}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                status = status_data.get("status")
                
                # Show progress every 5 seconds
                if i % 5 == 0:
                    progress_info = status_data.get("progress", {})
                    elapsed = time.time() - start_time
                    if progress_info:
                        progress_status = progress_info.get("status", "")
                        print(f"   [{elapsed:5.1f}s] {status} - {progress_status}")
                    else:
                        print(f"   [{elapsed:5.1f}s] {status}")

                if status == "completed":
                    result = status_data.get("result", {})
                    filename = result.get('filename', 'N/A')
                    elapsed = time.time() - start_time
                    
                    print(f"\n🎉 Premium Generation Complete!")
                    print(f"   Time: {result.get('generation_time', elapsed):.1f}s")
                    print(f"   File: {filename}")
                    
                    # Show detailed parameters used
                    params = result.get('parameters', {})
                    print(f"   Final prompt: '{params.get('prompt', 'N/A')[:100]}{'...' if len(params.get('prompt', '')) > 100 else ''}'")
                    print(f"   Resolution: {params.get('width', 'N/A')}x{params.get('height', 'N/A')}")
                    print(f"   Steps: {params.get('num_inference_steps', 'N/A')}")
                    print(f"   Guidance: {params.get('guidance_scale', 'N/A')}")
                    
                    # Show system info
                    sys_info = result.get('system_info', {})
                    if sys_info.get('gpu_name'):
                        print(f"   GPU: {sys_info['gpu_name']}")
                        if sys_info.get('gpu_memory_used'):
                            print(f"   GPU Memory: {sys_info['gpu_memory_used'] / (1024**3):.2f} GB")
                    
                    # Copy to premium test folder
                    if test_folder and filename != 'N/A':
                        try:
                            import shutil
                            source = f"data/outputs/{filename}"
                            if os.path.exists(source):
                                shutil.copy2(source, test_folder)
                                print(f"   Saved to: {test_folder}/{filename}")
                        except Exception as e:
                            print(f"   Warning: Could not copy image: {e}")
                    
                    return True, filename
                    
                elif status == "failed":
                    error = status_data.get("error", "Unknown error")
                    print(f"\n❌ Premium generation failed: {error}")
                    return False, None
                    
                elif status == "cancelled":
                    print(f"\n❌ Premium generation cancelled")
                    return False, None
            
            time.sleep(1)
        
        print(f"\n⏰ Timeout after 10 minutes")
        return False, None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None


def run_premium_quality_tests():
    """Run premium quality tests with ultra-high quality settings."""
    
    print("🏆 ULTRA-PREMIUM QUALITY Image Generation Tests")
    print("🎨 Museum/Gallery Quality - Maximum Detail Settings")
    print("=" * 65)
    
    # Create premium test folder
    test_folder = create_premium_test_folder()
    print(f"📁 Premium test folder: {test_folder}")
    print()
    
    premium_test_cases = [
        {
            "name": "Museum-Quality Portrait",
            "prompt": "museum quality oil painting style portrait of a distinguished elderly scholar with intricate silver beard, piercing intelligent blue eyes with visible iris patterns and natural reflections, deeply weathered skin showing life experience with natural wrinkles and age spots, wearing an elegant charcoal wool suit with subtle texture, dramatic chiaroscuro lighting with soft shadows, hyperrealistic detail in every pore and hair strand, masterpiece quality, 16k resolution, award winning fine art photography",
            "negative_prompt": "cartoon, anime, digital painting, artificial, plastic skin, oversaturated, smooth skin, young, modern, casual clothing, harsh lighting, amateur, low quality, blurry, distorted",
            "steps": 80,
            "guidance": 9.5,
            "seed": 2001
        },
        {
            "name": "Scientific Macro Photography",
            "prompt": "award winning scientific macro photograph of a perfect dewdrop suspended on an intricate spider web at golden hour sunrise, each individual silk strand visible with microscopic detail, rainbow light refraction creating prismatic effects within the crystal clear water droplet, background bokeh of morning meadow flowers, shot with professional macro lens at f/2.8, ultra sharp focus, National Geographic quality, museum exhibition worthy, 16k resolution, perfect exposure and composition",
            "negative_prompt": "blurry, artificial, cartoon, painting, low resolution, multiple droplets, cluttered, oversaturated, amateur, digital art, unrealistic, distorted",
            "steps": 70,
            "guidance": 9.0,
            "seed": 2002
        },
        {
            "name": "Architectural Photography Masterpiece",
            "prompt": "breathtaking architectural photography masterpiece of a cutting-edge glass and titanium skyscraper with complex geometric facade patterns, each window reflecting the dramatic golden hour sky with volumetric cloud formations, perfect symmetry and proportions, shot with tilt-shift lens for maximum sharpness, professional architectural photography with perfect perspective correction, award winning composition, featured in Architectural Digest, museum quality print, 16k resolution, flawless technical execution",
            "negative_prompt": "blurry, distorted, unrealistic proportions, cartoon, low quality, people, cars, amateur photography, oversaturated, digital art, painting, cluttered",
            "steps": 65,
            "guidance": 8.5,
            "seed": 2003
        },
        {
            "name": "Fine Art Still Life",
            "prompt": "exquisite fine art still life composition featuring a single perfect red rose with velvety petals showing intricate natural texture and subtle color variations, morning dew droplets catching soft natural light, placed on aged weathered wood surface with beautiful grain patterns, dramatic side lighting creating perfect shadows and highlights, shot with medium format camera, museum quality fine art photography, reminiscent of Dutch Golden Age paintings, 16k resolution, gallery exhibition worthy",
            "negative_prompt": "multiple flowers, artificial, plastic, cartoon, digital art, oversaturated, modern, cluttered, amateur, low quality, blurry, harsh lighting, unrealistic",
            "steps": 75,
            "guidance": 9.0,
            "seed": 2004
        }
    ]
    
    successful_tests = 0
    total_tests = len(premium_test_cases)

    print(f"⚡ Ultra-Premium Settings:")
    print(f"   • Inference Steps: 65-80 (vs standard 20-30)")
    print(f"   • Guidance Scale: 8.5-9.5 (vs standard 7.5)")
    print(f"   • Resolution: 1024x1024 with maximum detail")
    print(f"   • Expected time: 60-120 seconds per image")
    print()
    generated_files = []
    
    for i, test_case in enumerate(premium_test_cases, 1):
        print(f"\n[{i}/{total_tests}] {test_case['name']}")
        print("=" * 40)
        
        success, filename = generate_premium_image(
            prompt=test_case["prompt"],
            negative_prompt=test_case["negative_prompt"],
            steps=test_case["steps"],
            guidance=test_case["guidance"],
            seed=test_case["seed"],
            test_folder=test_folder
        )
        
        if success:
            successful_tests += 1
            if filename:
                generated_files.append(filename)
        
        # Longer delay between premium tests
        if i < total_tests:
            print("\n⏳ Waiting before next premium generation...")
            time.sleep(5)
    
    # Summary
    print(f"\n🏆 Premium Test Results Summary")
    print("=" * 35)
    print(f"Successful: {successful_tests}/{total_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print("🎉 All premium tests passed!")
        print(f"📁 Premium images in: {test_folder}")
        print(f"📊 Generated {len(generated_files)} premium images:")
        for filename in generated_files:
            print(f"   🖼️  {filename}")
    else:
        print("⚠️  Some premium tests failed.")
        if generated_files:
            print(f"📁 Partial results in: {test_folder}")
    
    return successful_tests == total_tests


def main():
    """Main entry point for premium quality testing."""
    try:
        success = run_premium_quality_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Premium test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
