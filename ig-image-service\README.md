# GPU-Accelerated Image Generation Service

🚀 **Production-ready API service for high-quality AI image generation with encryption support**

## 🏗️ Architecture

- **API Service**: FastAPI server with async endpoints
- **Worker Service**: Celery GPU worker with SDXL pipeline
- **Redis**: Message broker and result backend
- **Storage**: Persistent image storage with optional encryption
- **GPU Support**: NVIDIA CUDA 12.4+ with optimized memory usage

## ⚡ Quick Start

```bash
# 1. Start the service
docker compose up -d

# 2. Verify deployment
curl http://localhost:8000/health

# 3. Test with encryption
python scripts/enhanced_workflow_test.py
```

## 📋 API Endpoints

### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-08-06T18:30:00.000Z",
  "version": "1.0.0",
  "gpu_available": true
}
```

### System Information
```http
GET /system/info
```
**Response:**
```json
{
  "service": "Image Generation Service",
  "version": "1.0.0",
  "gpu": {
    "available": true,
    "name": "NVIDIA GeForce RTX 2080 Ti",
    "memory_total": "11GB"
  }
}
```

### Create Task
```http
POST /tasks
Content-Type: application/json
```

**Request Body:**
```json
{
  "prompt": "professional portrait of elderly man with silver beard",
  "negative_prompt": "cartoon, anime, painting, low quality, blurry",
  "width": 1024,
  "height": 1024,
  "num_inference_steps": 50,
  "guidance_scale": 8.0,
  "seed": 12345,
  "persona": "default",
  "encrypt": true,
  "encryption_key": "base64-encoded-32-byte-key-optional"
}
```

**Response:**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "queued",
  "encrypted": true,
  "encryption_key": "auto-generated-key-if-not-provided",
  "created_at": "2025-08-06T18:30:00.000Z"
}
```

### Get Task Status
```http
GET /tasks/{task_id}
```

**Response (Processing):**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "processing",
  "progress": {
    "status": "generating_image",
    "progress": 50
  },
  "created_at": "2025-08-06T18:30:00.000Z"
}
```

**Response (Completed):**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "result": {
    "filename": "generated_550e8400_abc123.png",
    "generation_time": 45.2,
    "parameters": {
      "prompt": "professional portrait...",
      "width": 1024,
      "height": 1024,
      "num_inference_steps": 50,
      "guidance_scale": 8.0,
      "encrypt": true
    },
    "system_info": {
      "device": "cuda",
      "gpu_name": "NVIDIA GeForce RTX 2080 Ti",
      "gpu_memory_used": 7065516032
    }
  },
  "created_at": "2025-08-06T18:30:00.000Z",
  "completed_at": "2025-08-06T18:30:45.200Z"
}
```

### Download Image
```http
GET /tasks/{task_id}/download?decryption_key=base64-key
```

**Parameters:**
- `decryption_key` (optional): Required for encrypted images

**Response:**
- **Content-Type**: `image/png`
- **Content-Disposition**: `attachment; filename="image.png"`
- **Body**: Binary image data (decrypted if encrypted)

### List Tasks
```http
GET /tasks?limit=10&offset=0
```

**Response:**
```json
{
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-************",
      "status": "completed",
      "created_at": "2025-08-06T18:30:00.000Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

## 🔧 Request Parameters

### Required Parameters
- **`prompt`** (string, 1-1000 chars): Text description of desired image

### Optional Parameters
- **`negative_prompt`** (string, max 500 chars): Elements to avoid
- **`width`** (integer, 256-2048): Image width in pixels (default: 1024)
- **`height`** (integer, 256-2048): Image height in pixels (default: 1024)
- **`num_inference_steps`** (integer, 1-100): Denoising steps for quality (default: 30)
- **`guidance_scale`** (float, 0.1-20.0): Prompt adherence strength (default: 8.0)
- **`seed`** (integer): Random seed for reproducible results
- **`persona`** (string): Style preset to apply (default: "default")
- **`encrypt`** (boolean): Enable image encryption (default: false)
- **`encryption_key`** (string): Base64-encoded 32-byte key (auto-generated if not provided)

### Quality Settings Guide
- **Fast Generation**: steps=20-30, guidance=7.0-8.0
- **High Quality**: steps=40-60, guidance=8.0-9.0
- **Ultra Premium**: steps=65-80, guidance=8.5-9.5

## 🔄 Workflow

### Standard Workflow
1. **Create Task** → Submit generation request
2. **Monitor Status** → Poll task status endpoint
3. **Download Result** → Retrieve completed image

### Encrypted Workflow
1. **Create Encrypted Task** → Set `encrypt: true`
2. **Store Encryption Key** → Save returned key securely
3. **Monitor Status** → Same as standard workflow
4. **Download & Decrypt** → Provide decryption key

### Example Workflow (Python)
```python
import requests
import time

# 1. Create task
response = requests.post("http://localhost:8000/tasks", json={
    "prompt": "beautiful sunset over mountains",
    "num_inference_steps": 50,
    "encrypt": True
})
task = response.json()
task_id = task["task_id"]
encryption_key = task["encryption_key"]

# 2. Monitor progress
while True:
    status = requests.get(f"http://localhost:8000/tasks/{task_id}").json()
    if status["status"] == "completed":
        break
    elif status["status"] == "failed":
        print("Task failed:", status.get("error"))
        exit(1)
    time.sleep(5)

# 3. Download result
image_response = requests.get(
    f"http://localhost:8000/tasks/{task_id}/download",
    params={"decryption_key": encryption_key}
)
with open("result.png", "wb") as f:
    f.write(image_response.content)
```

## 🔐 Encryption Features

### Automatic Key Generation
- System generates secure 32-byte keys automatically
- Keys returned in task creation response
- Client responsible for key storage

### Client-Provided Keys
- Provide base64-encoded 32-byte key in request
- Key validation ensures proper format
- Same key required for decryption

### Security Benefits
- Images encrypted at rest in container
- Only client with key can decrypt
- Secure key exchange via HTTPS (recommended)

## 🚀 Deployment

### Docker Compose (Recommended)
```bash
# Build and start
docker compose build
docker compose up -d

# Scale workers
docker compose up -d --scale worker=3

# View logs
docker compose logs worker
```

### Environment Variables
```bash
# GPU selection
CUDA_VISIBLE_DEVICES=0,1

# Redis configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/1

# Storage paths
OUTPUT_DIR=/data/outputs
PERSONA_DIR=/data/personas
```

### Requirements
- **Hardware**: NVIDIA GPU with 6GB+ VRAM, 16GB+ RAM
- **Software**: Docker with NVIDIA Container Toolkit
- **Network**: Port 8000 available

## 📊 Performance

### Generation Times
- **Standard Quality** (20-30 steps): 15-30 seconds
- **High Quality** (40-60 steps): 30-60 seconds
- **Ultra Premium** (65-80 steps): 45-90 seconds

### Throughput
- **Single GPU**: 2-4 images/minute
- **Multi-GPU**: Linear scaling
- **Concurrent Tasks**: Queue-based processing

## 🛠️ Testing

### Run Test Suite
```bash
# Enhanced workflow test (with encryption)
python scripts/enhanced_workflow_test.py

# Premium quality test
python scripts/premium_quality_test.py

# Quick test
python scripts/quick_test.py "test prompt"
```

### Test Results Location
- **Workflow Tests**: `data/workflow_tests/`
- **Premium Tests**: `data/premium_tests/`
- **Output Images**: `data/outputs/`

## 🔧 Configuration

### docker-compose.yml
```yaml
services:
  api:
    ports:
      - "8000:8000"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0

  worker:
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

### Scaling Configuration
```bash
# Multiple workers
docker compose up -d --scale worker=3

# Multiple API instances
docker compose up -d --scale api=2

# Specific GPU allocation
CUDA_VISIBLE_DEVICES=1,2 docker compose up -d
```

## 🆘 Troubleshooting

### Common Issues
1. **GPU not detected**: Check `nvidia-smi` and NVIDIA Container Toolkit
2. **Port conflicts**: Change port in docker-compose.yml
3. **Memory issues**: Reduce worker concurrency or inference steps
4. **Encryption errors**: Verify key format (base64, 32 bytes)

### Debug Commands
```bash
# Check service status
docker compose ps

# View logs
docker compose logs worker --tail=50

# Test GPU access
docker run --gpus all nvidia/cuda:12.4.1-base-ubuntu22.04 nvidia-smi

# Monitor resources
docker stats
nvidia-smi
```

## 📄 License

MIT License - See source code for details.

---

**Version**: 1.0.0
**CUDA Support**: 12.4+
**Tested On**: Ubuntu 22.04, Docker 24.0+