# Image Generation Service Configuration
# Copy this file to .env and modify as needed

# Redis Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/1

# Model Paths
SD_MODEL_PATH=/models/sdxl_lightning
PERSONA_DIR=/data/personas
OUTPUT_DIR=/data/outputs

# GPU Configuration
CUDA_VISIBLE_DEVICES=0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=2

# Worker Configuration
WORKER_CONCURRENCY=1
WORKER_LOGLEVEL=INFO

# Security (for production)
# API_SECRET_KEY=your-secret-key-here
# REDIS_PASSWORD=your-redis-password

# Monitoring (optional)
# SENTRY_DSN=your-sentry-dsn
# LOG_LEVEL=INFO
