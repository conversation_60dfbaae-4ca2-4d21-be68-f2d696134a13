# docker-compose.yml

```yaml
version: "3.9"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 2
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      SD_MODEL_PATH: /models/sdxl_lightning
      PERSONA_DIR: /data/personas
      OUTPUT_DIR: /data/outputs
      CUDA_VISIBLE_DEVICES: "0"
    volumes:
      - ./data:/data
      - ./models:/models:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: ["gpu"]
    depends_on:
      - redis

  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    command: celery -A app.worker worker --loglevel=INFO --concurrency=1 -Q default -n gpu_worker@%h
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      SD_MODEL_PATH: /models/sdxl_lightning
      PERSONA_DIR: /data/personas
      OUTPUT_DIR: /data/outputs
      CUDA_VISIBLE_DEVICES: "0"
    volumes:
      - ./data:/data
      - ./models:/models:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: ["gpu"]
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  data:
  models:
```

---

# Dockerfile

```Dockerfile
# syntax=docker/dockerfile:1.6
FROM nvidia/cuda:12.4.1-devel-ubuntu22.04 AS base

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y --no-install-recommends \
        python3.11 python3.11-distutils python3-pip git curl ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies first (reduces rebuild time)
COPY requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir --upgrade pip && \
    pip3 install --no-cache-dir -r /tmp/requirements.txt

# ---------- Builder stage to download models ----------
FROM base AS model_download

# Use separate layer so model downloads are cached
RUN mkdir -p /models /app
WORKDIR /app

# Download SDXL Lightning & InstantID models via huggingface-cli or curl
# You can also pre-populate the ./models folder locally and mount it read-only.
# Example:
# RUN apt-get update && apt-get install -y git git-lfs && \
#     git lfs install && \
#     git clone https://huggingface.co/Lykon/sd_xl_lightning_4step_unet /models/sdxl_lightning

# ---------- Runtime stage ----------
FROM base AS runtime

ENV PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

WORKDIR /app

# Copy application code
COPY app/ /app/app/
COPY scripts/ /app/scripts/

# (Optional) copy downloaded models into runtime image
# COPY --from=model_download /models /models

EXPOSE 8000

HEALTHCHECK CMD curl --fail http://localhost:8000/health || exit 1

CMD ["bash"]
```

---

# requirements.txt (place next to Dockerfile)

```text
fastapi==0.111.0
uvicorn[standard]==0.30.0
celery==5.4.0
redis==5.0.4
diffusers==0.30.1
transformers==4.42.1
xformers==0.0.26.post1
accelerate==0.31.0
pillow>=10.0.0
instantid @ git+https://github.com/instantX-research/InstantID.git
# Pin torch to match CUDA 12.4
torch==2.2.2+cu124
triton==2.2.0
```

---

## Usage

```bash
# Build & start the stack
docker compose build
docker compose up -d

# Test the API health endpoint
curl http://localhost:8000/health  # -> {"status":"ok"}

# Submit a generation task
python scripts/demo_add_task.py  # sample script hitting POST /tasks
```

---

### Notes

- **Volumes**: `./data` keeps personas & outputs; `./models` caches model weights (you can pre‑download to avoid a slow first build).
- **GPU scheduling**: we pin both API & worker to the same RTX 2080 Ti (`CUDA_VISIBLE_DEVICES=0`) but only the worker will use heavy compute.
- **Dependencies**: if xformers fails to compile, append `--timeout=2000` to pip install or grab the pre‑built wheel.
- **Scaling**: spin up extra `worker` replicas by `docker compose up --scale worker=2`. Each one will reserve a GPU; with a single card keep it at 1.
- **Healthcheck**: Docker marks the `api` container unhealthy if the FastAPI endpoint doesn’t respond — useful for auto‑restart.

---

Let me know if you’d like:

1. A `docker-compose.override.yml` that mounts local source for hot‑reload during dev.
2. A sample `manifest.json` and the corresponding POST payload.
3. Kubernetes manifests for future scaling.

Feel free to tweak paths, environment vars, or model versions; we can iterate.

