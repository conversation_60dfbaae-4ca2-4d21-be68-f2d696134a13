"""
FastAPI entry-point for image generation service.
Provides health check and task management endpoints.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from celery import Celery
import os
import uuid
from datetime import datetime

app = FastAPI(
    title="Image Generation Service",
    version="1.0.0",
    description="GPU-accelerated image generation micro-service using SDXL Lightning",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Pydantic models for request/response validation
class ImageGenerationRequest(BaseModel):
    prompt: str = Field(..., description="Text prompt for image generation", min_length=1, max_length=1000)
    negative_prompt: Optional[str] = Field(None, description="Negative prompt to avoid certain elements", max_length=500)
    width: int = Field(1024, description="Image width in pixels", ge=256, le=2048)
    height: int = Field(1024, description="Image height in pixels", ge=256, le=2048)
    num_inference_steps: int = Field(30, description="Number of denoising steps (20-80 for premium quality)", ge=1, le=100)
    guidance_scale: float = Field(8.0, description="Guidance scale (7.0-12.0 for premium quality)", ge=0.1, le=20.0)
    seed: Optional[int] = Field(None, description="Random seed for reproducible results")
    persona: Optional[str] = Field("default", description="Persona/style to apply")
    encrypt: bool = Field(False, description="Enable encryption for generated image storage")
    encryption_key: Optional[str] = Field(None, description="Base64 encoded encryption key (32 bytes). If encrypt=True and no key provided, a random key will be generated")

class TaskResponse(BaseModel):
    task_id: str
    status: str
    encrypted: bool = False
    encryption_key: Optional[str] = None
    created_at: str

class TaskStatusResponse(BaseModel):
    task_id: str
    status: str
    result: Optional[Dict[Any, Any]] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    error: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    gpu_available: bool

# Celery configuration
celery_app = Celery(
    "image_generation",
    broker=os.getenv("CELERY_BROKER_URL", "redis://redis:6379/0"),
    backend=os.getenv("CELERY_RESULT_BACKEND", "redis://redis:6379/1")
)


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with system information."""
    try:
        # Check if we can connect to Redis
        redis_status = "ok"
        try:
            celery_app.control.inspect().ping()
        except Exception:
            redis_status = "redis_unavailable"

        # Check GPU availability (basic check)
        gpu_available = False
        try:
            import torch
            gpu_available = torch.cuda.is_available()
        except ImportError:
            pass

        return HealthResponse(
            status=redis_status,
            timestamp=datetime.utcnow().isoformat(),
            version="1.0.0",
            gpu_available=gpu_available
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@app.post("/tasks", response_model=TaskResponse)
async def create_task(request: ImageGenerationRequest):
    """Create a new image generation task."""
    try:
        # Generate unique task ID
        task_id = str(uuid.uuid4())
        created_at = datetime.utcnow().isoformat()

        # Handle encryption
        encryption_key = None
        if request.encrypt:
            if request.encryption_key:
                # Validate provided key
                try:
                    import base64
                    decoded_key = base64.b64decode(request.encryption_key)
                    if len(decoded_key) != 32:
                        raise HTTPException(status_code=400, detail="Encryption key must be 32 bytes when base64 decoded")
                    encryption_key = request.encryption_key
                except Exception as e:
                    raise HTTPException(status_code=400, detail=f"Invalid encryption key: {str(e)}")
            else:
                # Generate random key
                import secrets
                import base64
                random_key = secrets.token_bytes(32)
                encryption_key = base64.b64encode(random_key).decode('utf-8')

        # Prepare task data
        task_data = {
            "task_id": task_id,
            "prompt": request.prompt,
            "negative_prompt": request.negative_prompt,
            "width": request.width,
            "height": request.height,
            "num_inference_steps": request.num_inference_steps,
            "guidance_scale": request.guidance_scale,
            "seed": request.seed,
            "persona": request.persona,
            "encrypt": request.encrypt,
            "encryption_key": encryption_key,
            "created_at": created_at
        }

        # Send task to Celery worker
        task = celery_app.send_task(
            "app.worker.generate_image",
            args=[task_data],
            task_id=task_id
        )

        return TaskResponse(
            task_id=task.id,
            status="queued",
            encrypted=request.encrypt,
            encryption_key=encryption_key if request.encrypt and not request.encryption_key else None,
            created_at=created_at
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")


@app.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """Get the status and result of a task."""
    try:
        result = celery_app.AsyncResult(task_id)

        # Map Celery states to our API states
        status_mapping = {
            "PENDING": "queued",
            "STARTED": "processing",
            "PROGRESS": "processing",
            "SUCCESS": "completed",
            "FAILURE": "failed",
            "RETRY": "processing",
            "REVOKED": "cancelled"
        }

        status = status_mapping.get(result.status, result.status.lower())

        response_data = {
            "task_id": task_id,
            "status": status
        }

        # Add progress information if available
        if result.status == "PROGRESS" and hasattr(result, 'info') and result.info:
            response_data["progress"] = result.info

        if result.ready():
            if result.successful():
                response_data["result"] = result.result
                if isinstance(result.result, dict) and "completed_at" in result.result:
                    response_data["completed_at"] = result.result["completed_at"]
            else:
                response_data["error"] = str(result.result)

        return TaskStatusResponse(**response_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")


@app.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a running or queued task."""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        return {"task_id": task_id, "status": "cancelled"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")


@app.get("/tasks")
async def list_tasks(limit: int = 10, offset: int = 0):
    """List recent tasks (requires Redis inspection)."""
    try:
        # This is a basic implementation - in production you'd want proper task storage
        inspect = celery_app.control.inspect()

        # Get active tasks
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()

        tasks = []

        # Process active tasks
        if active_tasks:
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        "task_id": task["id"],
                        "status": "processing",
                        "worker": worker,
                        "name": task["name"]
                    })

        # Process scheduled tasks
        if scheduled_tasks:
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        "task_id": task["request"]["id"],
                        "status": "queued",
                        "worker": worker,
                        "name": task["request"]["task"]
                    })

        # Apply pagination
        paginated_tasks = tasks[offset:offset + limit]

        return {
            "tasks": paginated_tasks,
            "total": len(tasks),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list tasks: {str(e)}")


@app.get("/tasks/{task_id}/download")
async def download_image(task_id: str, decryption_key: Optional[str] = None):
    """Download the generated image for a completed task."""
    try:
        # Check task status first
        result = celery_app.AsyncResult(task_id)

        if result.state != "SUCCESS":
            if result.state == "PENDING":
                raise HTTPException(status_code=404, detail="Task not found")
            elif result.state == "FAILURE":
                raise HTTPException(status_code=400, detail="Task failed, no image available")
            else:
                raise HTTPException(status_code=400, detail=f"Task not completed yet (status: {result.state})")

        # Get task result
        task_result = result.result
        if not task_result or "filename" not in task_result:
            raise HTTPException(status_code=404, detail="No image file found for this task")

        filename = task_result["filename"]
        file_path = f"/data/outputs/{filename}"

        # Check if file exists
        import os
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Image file not found on server")

        # Handle encryption/decryption
        task_data = task_result.get("parameters", {})
        is_encrypted = task_data.get("encrypt", False)

        if is_encrypted:
            if not decryption_key:
                raise HTTPException(status_code=400, detail="Decryption key required for encrypted image")

            # Decrypt and return image
            try:
                import base64
                from cryptography.fernet import Fernet

                # Read encrypted file
                with open(file_path, "rb") as f:
                    encrypted_data = f.read()

                # Decrypt
                key = base64.urlsafe_b64encode(base64.b64decode(decryption_key))
                fernet = Fernet(key)
                decrypted_data = fernet.decrypt(encrypted_data)

                # Return decrypted image
                from fastapi.responses import Response
                return Response(
                    content=decrypted_data,
                    media_type="image/png",
                    headers={"Content-Disposition": f"attachment; filename=decrypted_{filename}"}
                )

            except Exception as e:
                raise HTTPException(status_code=400, detail=f"Failed to decrypt image: {str(e)}")
        else:
            # Return unencrypted image
            from fastapi.responses import FileResponse
            return FileResponse(
                path=file_path,
                media_type="image/png",
                filename=filename
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download image: {str(e)}")


@app.get("/system/info")
async def system_info():
    """Get system information including GPU status."""
    try:
        info = {
            "service": "Image Generation Service",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat()
        }

        # GPU information
        try:
            import torch
            if torch.cuda.is_available():
                info["gpu"] = {
                    "available": True,
                    "device_count": torch.cuda.device_count(),
                    "current_device": torch.cuda.current_device(),
                    "device_name": torch.cuda.get_device_name(0),
                    "memory_allocated": torch.cuda.memory_allocated(0),
                    "memory_reserved": torch.cuda.memory_reserved(0),
                    "memory_total": torch.cuda.get_device_properties(0).total_memory
                }
            else:
                info["gpu"] = {"available": False}
        except ImportError:
            info["gpu"] = {"available": False, "error": "PyTorch not available"}

        # Worker information
        try:
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            info["workers"] = {
                "active": len(stats) if stats else 0,
                "stats": stats
            }
        except Exception:
            info["workers"] = {"active": 0, "error": "Unable to inspect workers"}

        return info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system info: {str(e)}")


def main():
    """Main entry point for the API service."""
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, workers=2)


if __name__ == "__main__":
    main()
