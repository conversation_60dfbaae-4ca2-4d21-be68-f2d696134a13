# syntax=docker/dockerfile:1.6
FROM nvidia/cuda:12.4.1-devel-ubuntu22.04 AS base

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y --no-install-recommends \
        python3.11 python3.11-distutils python3-pip git curl ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# ---- install Python deps early (layer caching) ----
COPY requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir --upgrade pip && \
    pip3 install --no-cache-dir -r /tmp/requirements.txt

# ---------- optional model-download stage ----------
FROM base AS model_download
RUN mkdir -p /models /app
WORKDIR /app
# (clone SDXL Lightning, InstantID, etc., if you don't plan to bind-mount models)

# ---------- runtime stage ----------
FROM base AS runtime
ENV PYTHONUNBUFFERED=1 PYTHONPATH=/app
WORKDIR /app

COPY app/ /app/app/
COPY scripts/ /app/scripts/
# COPY --from=model_download /models /models   # uncomment if used

EXPOSE 8000
HEALTHCHECK CMD curl --fail http://localhost:8000/health || exit 1
CMD ["bash"]
