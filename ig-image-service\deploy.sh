#!/bin/bash

# Image Generation Service - Export Deployment Script
# Usage: ./deploy.sh [build|start|stop|restart|logs|test|help]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
Image Generation Service - Export Deployment Script

Usage: $0 [COMMAND]

Commands:
    build       Build Docker images
    start       Start all services
    stop        Stop all services
    restart     Restart all services
    logs        Show service logs
    test        Test the deployed service
    status      Show service status
    clean       Clean up containers and images
    help        Show this help message

Examples:
    $0 build       # Build the images
    $0 start       # Start services
    $0 test        # Test the service
    $0 logs        # View logs

EOF
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check NVIDIA GPU
    if command -v nvidia-smi &> /dev/null; then
        log_success "NVIDIA GPU detected"
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader | head -1
    else
        log_warning "nvidia-smi not found. GPU acceleration may not work"
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check NVIDIA Container Toolkit
    if docker info 2>/dev/null | grep -q "nvidia"; then
        log_success "NVIDIA Container Toolkit detected"
    else
        log_warning "NVIDIA Container Toolkit may not be installed"
    fi
    
    log_success "Requirements check completed"
}

build_images() {
    log_info "Building Docker images..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose build
    else
        docker compose build
    fi
    
    log_success "Images built successfully"
}

start_services() {
    log_info "Starting services..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    log_info "Waiting for services to start..."
    sleep 10
    
    # Check if services are running
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
    
    log_success "Services started successfully"
}

stop_services() {
    log_info "Stopping services..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        docker compose down
    fi
    
    log_success "Services stopped successfully"
}

restart_services() {
    log_info "Restarting services..."
    stop_services
    start_services
}

show_logs() {
    log_info "Showing service logs..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose logs -f
    else
        docker compose logs -f
    fi
}

show_status() {
    log_info "Service status:"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

test_service() {
    log_info "Testing service deployment..."
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health | grep -q "ok"; then
            log_success "Health endpoint is responding"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Health endpoint is not responding after 30 attempts"
            return 1
        fi
        echo "Waiting for service... ($i/30)"
        sleep 2
    done
    
    # Test task creation
    log_info "Testing task creation..."
    TASK_RESPONSE=$(curl -s -X POST http://localhost:8000/tasks \
        -H "Content-Type: application/json" \
        -d '{"prompt": "test deployment", "width": 512, "height": 512}')
    
    if echo "$TASK_RESPONSE" | grep -q "task_id"; then
        log_success "Task creation is working"
        TASK_ID=$(echo "$TASK_RESPONSE" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
        log_info "Created task ID: $TASK_ID"
    else
        log_error "Task creation failed"
        echo "Response: $TASK_RESPONSE"
        return 1
    fi
    
    # Test Python demo script
    if [ -f "scripts/demo_add_task.py" ]; then
        log_info "Testing with demo script..."
        if python3 scripts/demo_add_task.py; then
            log_success "Demo script executed successfully"
        else
            log_warning "Demo script failed (this may be normal if dependencies are missing)"
        fi
    fi
    
    log_success "Service testing completed successfully"
    
    echo ""
    echo "🎉 Deployment is ready!"
    echo "📍 Service URL: http://localhost:8000"
    echo "🔍 Health check: curl http://localhost:8000/health"
    echo "📊 View logs: $0 logs"
    echo "⏹️  Stop services: $0 stop"
}

clean_up() {
    log_info "Cleaning up containers and images..."
    
    # Stop and remove containers
    if command -v docker-compose &> /dev/null; then
        docker-compose down --rmi all --volumes --remove-orphans
    else
        docker compose down --rmi all --volumes --remove-orphans
    fi
    
    # Clean up Docker system
    docker system prune -f
    
    log_success "Cleanup completed"
}

# Main script logic
case "${1:-}" in
    build)
        check_requirements
        build_images
        ;;
    start)
        check_requirements
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    test)
        test_service
        ;;
    clean)
        clean_up
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        log_info "Image Generation Service - Export Deployment"
        echo ""
        check_requirements
        echo ""
        log_info "Building and starting services..."
        build_images
        start_services
        echo ""
        test_service
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
