#!/usr/bin/env python3
"""
Quick test script for generating single images with custom prompts.
"""

import requests
import time
import sys
import argparse


def generate_image(prompt, negative_prompt="", steps=20, width=1024, height=1024, seed=None):
    """Generate a single image with the given parameters."""
    
    task_data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": width,
        "height": height,
        "num_inference_steps": steps,
        "guidance_scale": 7.5,  # Higher guidance for better quality
        "seed": seed,
        "persona": "default"
    }
    
    print(f"🎨 Generating Image")
    print(f"   Prompt: '{prompt}'")
    if negative_prompt:
        print(f"   Negative: '{negative_prompt}'")
    print(f"   Size: {width}x{height}, Steps: {steps}")
    if seed:
        print(f"   Seed: {seed}")
    print("-" * 50)
    
    try:
        # Create task
        print("📤 Submitting task...")
        response = requests.post("http://localhost:8000/tasks", json=task_data, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to create task: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Task created: {task_id}")
        
        # Monitor progress
        print("⏳ Generating...")
        start_time = time.time()
        
        for i in range(120):  # 2 minutes max
            status_response = requests.get(f"http://localhost:8000/tasks/{task_id}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                status = status_data.get("status")
                
                # Show progress
                progress_info = status_data.get("progress", {})
                if progress_info and i % 3 == 0:  # Every 3 seconds
                    progress_status = progress_info.get("status", "")
                    elapsed = time.time() - start_time
                    print(f"   [{elapsed:4.1f}s] {progress_status}")

                if status == "completed":
                    result = status_data.get("result", {})
                    elapsed = time.time() - start_time
                    
                    print(f"\n🎉 Generation Complete!")
                    print(f"   Time: {result.get('generation_time', elapsed):.1f}s")
                    print(f"   File: {result.get('filename', 'N/A')}")
                    print(f"   Path: data/outputs/{result.get('filename', 'N/A')}")
                    
                    # Show system info
                    sys_info = result.get('system_info', {})
                    if sys_info.get('gpu_name'):
                        print(f"   GPU: {sys_info['gpu_name']}")
                    
                    return True
                    
                elif status == "failed":
                    error = status_data.get("error", "Unknown error")
                    print(f"\n❌ Generation Failed: {error}")
                    return False
                    
                elif status == "cancelled":
                    print(f"\n❌ Generation Cancelled")
                    return False
            
            time.sleep(1)
        
        print(f"\n⏰ Timeout after 2 minutes")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the service at http://localhost:8000")
        print("   Make sure the service is running with: docker compose up -d")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Quick image generation test")
    parser.add_argument("prompt", help="Text prompt for image generation")
    parser.add_argument("--negative", "-n", default="blurry, low quality, distorted", 
                       help="Negative prompt (default: 'blurry, low quality, distorted')")
    parser.add_argument("--steps", "-s", type=int, default=20,
                       help="Number of inference steps (default: 20)")
    parser.add_argument("--width", "-w", type=int, default=1024, 
                       help="Image width (default: 1024)")
    parser.add_argument("--height", type=int, default=1024,
                       help="Image height (default: 1024)")
    parser.add_argument("--seed", type=int, help="Random seed for reproducible results")
    
    args = parser.parse_args()
    
    success = generate_image(
        prompt=args.prompt,
        negative_prompt=args.negative,
        steps=args.steps,
        width=args.width,
        height=args.height,
        seed=args.seed
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
