#!/usr/bin/env python3
"""
Ultra-Premium Quality Image Generation Test Launcher
Museum/Gallery Quality - Maximum Detail Settings
"""

import subprocess
import sys
import os

def main():
    print("🏆 ULTRA-PREMIUM QUALITY Image Generation")
    print("🎨 Museum/Gallery Quality - Maximum Detail Settings")
    print("=" * 65)
    print()
    print("⚡ Ultra-Premium Settings:")
    print("   • Inference Steps: 65-80 (vs standard 20-30)")
    print("   • Guidance Scale: 10.5-12.0 (vs standard 7.5)")
    print("   • Resolution: 1024x1024 with maximum detail")
    print("   • Expected time: 60-120 seconds per image")
    print("   • 4 museum-quality test cases")
    print()
    
    # Check if service is running
    print("🔍 Checking service status...")
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Service is running")
        else:
            print("❌ Service is not responding properly")
            sys.exit(1)
    except Exception:
        print("❌ Service is not running. Please start with: docker compose up -d")
        sys.exit(1)
    
    print()
    input("Press Enter to start ultra-premium quality generation...")
    print()
    
    # Run the premium test
    try:
        result = subprocess.run([
            sys.executable, 
            "scripts/premium_quality_test.py"
        ], check=True)
        
        print("\n🎉 Ultra-premium quality test completed!")
        print("📁 Check the generated premium_tests folder for results")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Test failed with exit code {e.returncode}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
